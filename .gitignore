# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Wrangler
.wrangler/
dist/
worker/

# Environment variables
.env
.env.local
.env.production

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Build outputs
build/
dist/
out/

# Temporary files
tmp/
temp/