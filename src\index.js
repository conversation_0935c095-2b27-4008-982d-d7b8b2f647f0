/**
 * Cloudflare Workers - Clash 订阅链接聚合和转换服务
 *
 * 环境变量配置需求：
 * - SECRET_KEY: 身份验证密钥
 * - SUB_URLS: JSON格式的订阅链接数组，如 ["https://example.com/sub1", "https://example.com/sub2"]
 * - PRIVATE_PROXIES: JSON格式的私有代理列表，会添加到返回列表的最前面（可选）
 *
 * KV 命名空间配置需求：
 * - SUB_CACHE: 用于缓存订阅内容的KV命名空间
 */

// 导入 YAML 解析库 (使用 ES modules)
import YAML from 'js-yaml';

export default {
    async fetch(request, env, ctx) {
        try {
            // 1. 身份验证
            const url = new URL(request.url);
            const providedKey = url.searchParams.get('key');

            if (!providedKey || providedKey !== env.SECRET_KEY) {
                return new Response('Unauthorized: Invalid or missing key parameter', {
                    status: 403,
                    headers: { 'Content-Type': 'text/plain; charset=utf-8' }
                });
            }

            // 2. 解析订阅链接列表
            let subUrls = [];
            try {
                subUrls = env.SUB_URLS || {};
            } catch (error) {
                console.error('Failed to parse SUB_URLS:', error);
                return new Response('Server Configuration Error: Invalid SUB_URLS format', {
                    status: 500,
                    headers: { 'Content-Type': 'text/plain; charset=utf-8' }
                });
            }

            if (!Array.isArray(subUrls) || subUrls.length === 0) {
                console.log('No subscription URLs configured, returning empty proxies list');
                return createYamlResponse([]);
            }

            console.log(`Processing ${subUrls.length} subscription URLs`);

            // 3. 并发处理所有订阅链接
            const results = await Promise.allSettled(
                subUrls.map(url => processSubscription(url, env.SUB_CACHE))
            );

            // 4. 合并所有成功获取的 proxies
            let allProxies = [];
            let successCount = 0;
            let cacheCount = 0;
            let failCount = 0;

            for (let i = 0; i < results.length; i++) {
                const result = results[i];
                if (result.status === 'fulfilled') {
                    const { proxies, source } = result.value;
                    allProxies = allProxies.concat(proxies);

                    if (source === 'fresh') successCount++;
                    else if (source === 'cache') cacheCount++;
                } else {
                    failCount++;
                    console.error(`Subscription ${subUrls[i]} failed:`, result.reason);
                }
            }

            console.log(`Processing complete: ${successCount} fresh, ${cacheCount} cached, ${failCount} failed, ${allProxies.length} total proxies`);

            // 4.5. 添加私有代理到列表最前面
            const privateProxies = parsePrivateProxies(env.PRIVATE_PROXIES);
            if (privateProxies.length > 0) {
                allProxies = privateProxies.concat(allProxies);
                console.log(`Added ${privateProxies.length} private proxies, total proxies: ${allProxies.length}`);
            }

            // 5. 去重和重命名处理
            const deduplicatedProxies = deduplicateProxies(allProxies);
            console.log(`After deduplication: ${deduplicatedProxies.length} proxies`);

            // 6. 返回 YAML 格式结果
            return createYamlResponse(deduplicatedProxies);

        } catch (error) {
            console.error('Worker error:', error);
            return new Response('Internal Server Error', {
                status: 500,
                headers: { 'Content-Type': 'text/plain; charset=utf-8' }
            });
        }
    }
};

function base58Encode(url) {
  const alphabet = '123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz';
  const bytes = new TextEncoder().encode(url);
  let num = BigInt(0);
  
  for (let byte of bytes) {
    num = num * BigInt(256) + BigInt(byte);
  }
  
  let encoded = '';
  while (num > 0) {
    const remainder = Number(num % BigInt(58));
    encoded = alphabet[remainder] + encoded;
    num = num / BigInt(58);
  }
  
  return encoded;
}

/**
 * 处理单个订阅链接
 * @param {string} url - 订阅链接
 * @param {KVNamespace} kvNamespace - KV 存储命名空间
 * @returns {Promise<{proxies: Array, source: string}>} - 返回代理列表和数据源类型
 */
async function processSubscription(url, kvNamespace) {
    // 使用 URL 的 base64 编码作为缓存键
    const cacheKey = `sub_${base58Encode(url)}`;

    try {
        console.log(`Fetching subscription: ${url}`);

        // 尝试请求订阅链接
        const response = await fetch(url, {
            headers: {
                'User-Agent': 'Clash/v1.18.0',
                'Accept': 'text/yaml, application/yaml, text/plain, */*'
            },
            // 设置超时
            signal: AbortSignal.timeout(15000) // 15秒超时
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const yamlText = await response.text();

        if (!yamlText.trim()) {
            throw new Error('Empty response body');
        }

        // 尝试解析 YAML
        let parsed;
        try {
            parsed = YAML.load(yamlText);
        } catch (yamlError) {
            throw new Error(`YAML parsing failed: ${yamlError.message}`);
        }

        // 验证数据结构
        if (!parsed || typeof parsed !== 'object') {
            throw new Error('Invalid YAML structure: not an object');
        }

        if (!Array.isArray(parsed.proxies)) {
            throw new Error('Invalid YAML structure: proxies is not an array');
        }

        console.log(`Successfully fetched ${parsed.proxies.length} proxies from ${url}`);

        // 成功！将结果缓存到 KV
        try {
            await kvNamespace.put(cacheKey, yamlText);
            console.log(`Cached subscription result for ${url}`);
        } catch (cacheError) {
            console.error(`Failed to cache subscription ${url}:`, cacheError);
            // 缓存失败不影响主流程
        }

        return {
            proxies: parsed.proxies || [],
            source: 'fresh'
        };

    } catch (error) {
        console.log(`Failed to fetch ${url}: ${error.message}, attempting to use cache`);

        // 请求失败，尝试从缓存获取
        try {
            const cached = await kvNamespace.get(cacheKey);
            if (cached) {
                const parsed = YAML.load(cached);
                if (parsed && Array.isArray(parsed.proxies)) {
                    console.log(`Using cached data for ${url}: ${parsed.proxies.length} proxies`);
                    return {
                        proxies: parsed.proxies,
                        source: 'cache'
                    };
                }
            }
        } catch (cacheError) {
            console.error(`Failed to read cache for ${url}:`, cacheError);
        }

        // 请求和缓存都失败
        console.log(`No valid data available for ${url}, returning empty array`);
        return {
            proxies: [],
            source: 'failed'
        };
    }
}

/**
 * 代理节点去重和重命名
 * @param {Array} proxies - 代理节点列表
 * @returns {Array} - 去重后的代理节点列表
 */
function deduplicateProxies(proxies) {
    const nameCount = new Map();
    const result = [];

    for (const proxy of proxies) {
        // 跳过无效的代理对象
        if (!proxy || typeof proxy !== 'object' || !proxy.name || typeof proxy.name !== 'string') {
            console.warn('Skipping invalid proxy object:', proxy);
            continue;
        }

        const originalName = proxy.name.trim();
        if (!originalName) {
            console.warn('Skipping proxy with empty name');
            continue;
        }

        let finalName = originalName;

        // 处理重名情况
        if (nameCount.has(originalName)) {
            const count = nameCount.get(originalName) + 1;
            nameCount.set(originalName, count);
            finalName = `${originalName}_${count}`;
        } else {
            nameCount.set(originalName, 1);
        }

        // 创建新的代理对象，避免修改原对象
        const newProxy = {
            ...proxy,
            name: finalName
        };

        result.push(newProxy);
    }

    return result;
}

/**
 * 解析和验证私有代理配置
 * @param {string} privateProxiesConfig - 私有代理配置的 JSON 字符串
 * @returns {Array} - 验证后的私有代理列表
 */
function parsePrivateProxies(privateProxiesConfig) {
    if (!privateProxiesConfig || typeof privateProxiesConfig !== 'string') {
        console.log('No private proxies configured');
        return [];
    }

    try {
        const privateProxies = JSON.parse(privateProxiesConfig);

        if (!Array.isArray(privateProxies)) {
            console.error('Private proxies configuration is not an array');
            return [];
        }

        // 验证每个私有代理的基本结构
        const validProxies = [];
        for (let i = 0; i < privateProxies.length; i++) {
            const proxy = privateProxies[i];

            // 检查必需字段
            if (!proxy || typeof proxy !== 'object') {
                console.warn(`Private proxy ${i} is not a valid object, skipping`);
                continue;
            }

            if (!proxy.name || typeof proxy.name !== 'string') {
                console.warn(`Private proxy ${i} missing or invalid name, skipping`);
                continue;
            }

            if (!proxy.type || typeof proxy.type !== 'string') {
                console.warn(`Private proxy ${i} (${proxy.name}) missing or invalid type, skipping`);
                continue;
            }

            if (!proxy.server || typeof proxy.server !== 'string') {
                console.warn(`Private proxy ${i} (${proxy.name}) missing or invalid server, skipping`);
                continue;
            }

            if (!proxy.port || typeof proxy.port !== 'number') {
                console.warn(`Private proxy ${i} (${proxy.name}) missing or invalid port, skipping`);
                continue;
            }

            // 添加前缀标识私有代理
            const validProxy = {
                ...proxy,
                name: `[RH] ${proxy.name}`
            };

            validProxies.push(validProxy);
        }

        console.log(`Loaded ${validProxies.length} valid private proxies`);
        return validProxies;

    } catch (error) {
        console.error('Failed to parse private proxies configuration:', error);
        return [];
    }
}

/**
 * 创建 YAML 格式的响应
 * @param {Array} proxies - 代理列表
 * @returns {Response} - HTTP 响应对象
 */
function createYamlResponse(proxies) {
    const data = { proxies: proxies };
    const yamlContent = YAML.dump(data, {
        indent: 2,
        lineWidth: -1, // 不限制行宽
        noRefs: true,  // 不使用引用
        sortKeys: false // 保持原有顺序
    });

    return new Response(yamlContent, {
        status: 200,
        headers: {
            'Content-Type': 'text/yaml; charset=utf-8',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        }
    });
}
