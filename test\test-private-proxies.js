/**
 * 测试私有代理解析功能
 */

// 模拟 js-yaml 库
const YAML = {
    dump: (data, options) => {
        return JSON.stringify(data, null, 2); // 简化版本，用于测试
    }
};

// 从主文件复制 parsePrivateProxies 函数
function parsePrivateProxies(privateProxiesConfig) {
    if (!privateProxiesConfig || typeof privateProxiesConfig !== 'string') {
        console.log('No private proxies configured');
        return [];
    }

    try {
        const privateProxies = JSON.parse(privateProxiesConfig);
        
        if (!Array.isArray(privateProxies)) {
            console.error('Private proxies configuration is not an array');
            return [];
        }

        // 验证每个私有代理的基本结构
        const validProxies = [];
        for (let i = 0; i < privateProxies.length; i++) {
            const proxy = privateProxies[i];
            
            // 检查必需字段
            if (!proxy || typeof proxy !== 'object') {
                console.warn(`Private proxy ${i} is not a valid object, skipping`);
                continue;
            }

            if (!proxy.name || typeof proxy.name !== 'string') {
                console.warn(`Private proxy ${i} missing or invalid name, skipping`);
                continue;
            }

            if (!proxy.type || typeof proxy.type !== 'string') {
                console.warn(`Private proxy ${i} (${proxy.name}) missing or invalid type, skipping`);
                continue;
            }

            if (!proxy.server || typeof proxy.server !== 'string') {
                console.warn(`Private proxy ${i} (${proxy.name}) missing or invalid server, skipping`);
                continue;
            }

            if (!proxy.port || typeof proxy.port !== 'number') {
                console.warn(`Private proxy ${i} (${proxy.name}) missing or invalid port, skipping`);
                continue;
            }

            // 添加前缀标识私有代理
            const validProxy = {
                ...proxy,
                name: `[Private] ${proxy.name}`
            };

            validProxies.push(validProxy);
        }

        console.log(`Loaded ${validProxies.length} valid private proxies`);
        return validProxies;

    } catch (error) {
        console.error('Failed to parse private proxies configuration:', error);
        return [];
    }
}

// 测试用例
function runTests() {
    console.log('=== 测试私有代理解析功能 ===\n');

    // 测试1: 正常配置
    console.log('测试1: 正常配置');
    const validConfig = JSON.stringify([
        {
            "name": "US-LosAngles",
            "type": "trojan",
            "server": "us1.proxy.com",
            "port": 4433,
            "sni": "us1.proxy.com",
            "password": "xb07ad37-xe01-x31d-a18b-x07ae1e987ae",
            "udp": true,
            "skip-cert-verify": false
        },
        {
            "name": "US-LosAngles-2",
            "type": "trojan",
            "server": "us2.proxy.com",
            "port": 4433,
            "sni": "us2.proxy.com",
            "password": "zb07ad37-ze01-z31d-a18b-z07ae1e987ae",
            "udp": true,
            "skip-cert-verify": false
        }
    ]);
    
    const result1 = parsePrivateProxies(validConfig);
    console.log(`结果: ${result1.length} 个代理`);
    console.log('代理名称:', result1.map(p => p.name));
    console.log('');

    // 测试2: 空配置
    console.log('测试2: 空配置');
    const result2 = parsePrivateProxies('');
    console.log(`结果: ${result2.length} 个代理\n`);

    // 测试3: 无效 JSON
    console.log('测试3: 无效 JSON');
    const result3 = parsePrivateProxies('invalid json');
    console.log(`结果: ${result3.length} 个代理\n`);

    // 测试4: 缺少必需字段
    console.log('测试4: 缺少必需字段');
    const invalidConfig = JSON.stringify([
        {
            "name": "Valid-Proxy",
            "type": "trojan",
            "server": "valid.proxy.com",
            "port": 4433
        },
        {
            "name": "Invalid-Proxy",
            "type": "trojan"
            // 缺少 server 和 port
        },
        {
            // 缺少 name
            "type": "trojan",
            "server": "another.proxy.com",
            "port": 4433
        }
    ]);
    
    const result4 = parsePrivateProxies(invalidConfig);
    console.log(`结果: ${result4.length} 个代理`);
    console.log('代理名称:', result4.map(p => p.name));
    console.log('');

    console.log('=== 测试完成 ===');
}

// 运行测试
runTests();
