# wrangler.toml

name = "clash-subscription-converter"
main = "src/index.js"
compatibility_date = "2024-01-01"

# KV 命名空间绑定
[[kv_namespaces]]
binding = "SUB_CACHE"
id = "fef73faffc7849ab8eacb3fdc377da70"
preview_id = "your-preview-kv-namespace-id"

[vars]
# 用于验证请求身份的密钥
SECRET_KEY = "6689228f-dba2-4b59-90fb-ece869e2e5c7"
# 订阅链接列表
SUB_URLS = [
    "https://api.v2tun-163cdn.top/sub?target=clash&filename=V2Tun&new_name=true&scv=true&tfo=false&clash.doh=true&emoji=true&udp=true&url=https%3A%2F%2Fapi.v2tun.li%2Fosubscribe.php%3Fsid%3D26647%26token%3Dm85XoIULYevA",
    "https://**************/link/gH4RnABgB9C37487?clash=1"
]
# 私有代理服务器
PRIVATE_PROXIES = '''[
  {
    "name": "Huawei Beijing",
    "type": "trojan",
    "password": "2b07ad37-4e01-431d-a18b-007ae1e987ae",
    "server": "desk.maikebuke.com",
    "sni": "desk.maikebuke.com",
    "port": 4433,
    "skip-cert-verify": false,
    "udp": true
  },
  {
    "name": "UCloud US",
    "type": "trojan",
    "password": "b2166e81-73db-4370-bb04-30717a9f18d5",
    "server": "usa.maikebuke.com",
    "sni": "usa.maikebuke.com",
    "port": 4433,
    "skip-cert-verify": false,
    "udp": true
  },
  {
    "name": "RackNerd LA-US",
    "type": "trojan",
    "password": "50fe8ae8-7b59-4202-835d-4e9f0b0f4c6d",
    "server": "la.us.maikebuke.com",
    "sni": "la.us.maikebuke.com",
    "port": 4433,
    "skip-cert-verify": false,
    "udp": true
  },
  {
    "name": "RackNerd Toronto",
    "type": "trojan",
    "password": "a5f247d2-f20c-45b5-95b5-a58f300659e8",
    "server": "toronto.maikebuke.com",
    "sni": "toronto.maikebuke.com",
    "port": 4433,
    "skip-cert-verify": false,
    "udp": true
  }
]'''
